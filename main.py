from pydantic import BaseModel
import io
import logging
import os
import tempfile
import subprocess
import asyncio
from typing import Optional, Dict, Any
from urllib.parse import urlparse
import base64
from contextlib import asynccontextmanager
import uvicorn
from fastapi import FastAPI, HTTPException, BackgroundTasks
from PIL import Image, ImageOps
from pdf2image import convert_from_path
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, BrowserContext
import aiohttp
import hashlib
import time
from concurrent.futures import ThreadPoolExecutor
import mimetypes

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global instances for performance
browser: Optional[Browser] = None
playwright_instance = None
libreoffice_path = None
http_session: Optional[aiohttp.ClientSession] = None
thread_pool: Optional[ThreadPoolExecutor] = None

# In-memory cache for thumbnails (simple LRU-like cache)
thumbnail_cache: Dict[str, Dict[str, Any]] = {}
MAX_CACHE_SIZE = 100
CACHE_TTL = 3600  # 1 hour


def get_cache_key(url: str, width: int, height: int) -> str:
    """Generate cache key for thumbnail"""
    return hashlib.md5(f"{url}:{width}:{height}".encode()).hexdigest()


def get_cached_thumbnail(cache_key: str) -> Optional[Dict[str, Any]]:
    """Get thumbnail from cache if not expired"""
    if cache_key in thumbnail_cache:
        cached_item = thumbnail_cache[cache_key]
        if time.time() - cached_item['timestamp'] < CACHE_TTL:
            return cached_item
        else:
            # Remove expired item
            del thumbnail_cache[cache_key]
    return None


def cache_thumbnail(cache_key: str, thumbnail_data: Dict[str, Any]):
    """Cache thumbnail with TTL"""
    # Simple LRU: remove oldest if cache is full
    if len(thumbnail_cache) >= MAX_CACHE_SIZE:
        oldest_key = min(thumbnail_cache.keys(),
                         key=lambda k: thumbnail_cache[k]['timestamp'])
        del thumbnail_cache[oldest_key]

    thumbnail_cache[cache_key] = {
        **thumbnail_data,
        'timestamp': time.time()
    }


def is_document_url(url: str) -> bool:
    """Check if URL likely points to a document"""
    document_extensions = {'.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
                           '.odt', '.ods', '.odp', '.rtf', '.txt'}
    parsed = urlparse(url)
    path_lower = parsed.path.lower()
    return any(path_lower.endswith(ext) for ext in document_extensions)


def find_libreoffice():
    """Find LibreOffice executable path"""
    global libreoffice_path

    common_paths = [
        "/usr/bin/soffice",
        "/usr/local/bin/soffice",
        "/opt/libreoffice/program/soffice",
    ]

    for path in common_paths:
        if os.path.exists(path):
            logger.info(f"Found LibreOffice at: {path}")
            libreoffice_path = path
            return path

    logger.warning(
        "LibreOffice not found in common locations, using default path")
    libreoffice_path = "/usr/bin/soffice"
    return libreoffice_path


async def download_file(url: str) -> Optional[bytes]:
    """Download file from URL using persistent connection"""
    global http_session

    if not http_session:
        logger.error("HTTP session not initialized")
        return None

    try:
        timeout = aiohttp.ClientTimeout(total=30, connect=10)
        async with http_session.get(url, timeout=timeout) as response:
            if response.status == 200:
                # Check content length to avoid downloading huge files
                content_length = response.headers.get('content-length')
                # 50MB limit
                if content_length and int(content_length) > 50 * 1024 * 1024:
                    logger.warning(f"File too large: {content_length} bytes")
                    return None

                return await response.read()
            else:
                logger.warning(
                    f"Failed to download file: HTTP {response.status}")
                return None
    except asyncio.TimeoutError:
        logger.warning(f"Download timeout for {url}")
        return None
    except Exception as e:
        logger.warning(f"Download failed: {str(e)}")
        return None


def _document_thumbnail_sync(content: bytes, width: int = 300, height: int = 300) -> Optional[bytes]:
    """Synchronous document thumbnail generation (runs in thread pool)"""
    global libreoffice_path

    if not libreoffice_path or not os.path.exists(libreoffice_path):
        logger.warning("LibreOffice not available for document conversion")
        return None

    try:
        with tempfile.TemporaryDirectory() as tmpdir:
            doc_path = os.path.join(tmpdir, "document")
            with open(doc_path, "wb") as f:
                f.write(content)

            # Convert to PDF with optimized settings
            result = subprocess.run(
                [
                    libreoffice_path,
                    "--headless",
                    "--convert-to",
                    "pdf",
                    "--outdir",
                    tmpdir,
                    doc_path,
                ],
                capture_output=True,
                timeout=60,  # Reduced timeout
                # Set HOME to avoid permission issues
                env={**os.environ, 'HOME': tmpdir}
            )

            if result.returncode != 0:
                logger.error(f"LibreOffice failed: {result.stderr.decode()}")
                return None

            # Find generated PDF
            pdf_files = [f for f in os.listdir(tmpdir) if f.endswith(".pdf")]
            if not pdf_files:
                return None

            pdf_path = os.path.join(tmpdir, pdf_files[0])

            # Convert PDF to image with optimized settings
            images = convert_from_path(
                pdf_path,
                first_page=1,
                last_page=1,
                dpi=150,  # Lower DPI for faster processing
                thread_count=2
            )
            if not images:
                return None

            # Process the image into thumbnail
            img = images[0]

            # Use faster resampling for better performance
            img.thumbnail((width, height), Image.Resampling.BILINEAR)

            if img.mode in ('RGBA', 'P'):
                img = img.convert('RGB')

            # Convert to bytes with optimized settings
            buffer = io.BytesIO()
            img.save(buffer, format="JPEG", quality=75, optimize=True)
            buffer.seek(0)

            return buffer.getvalue()

    except subprocess.TimeoutExpired:
        logger.warning("LibreOffice conversion timeout")
        return None
    except Exception as e:
        logger.warning(f"Document thumbnail attempt failed: {str(e)}")
        return None


async def document_thumbnail(content: bytes, width: int = 300, height: int = 300) -> Optional[bytes]:
    """Generate thumbnail from document content (async wrapper)"""
    global thread_pool

    if not thread_pool:
        logger.error("Thread pool not initialized")
        return None

    try:
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            thread_pool,
            _document_thumbnail_sync,
            content,
            width,
            height
        )
    except Exception as e:
        logger.error(f"Document thumbnail failed: {e}")
        return None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifecycle with performance optimizations"""
    global browser, playwright_instance, http_session, thread_pool

    # Startup
    try:
        logger.info("Starting optimized thumbnail service...")

        # Initialize HTTP session with connection pooling
        connector = aiohttp.TCPConnector(
            limit=100,  # Total connection pool size
            limit_per_host=30,  # Per-host connection limit
            ttl_dns_cache=300,  # DNS cache TTL
            use_dns_cache=True,
            keepalive_timeout=30,
            enable_cleanup_closed=True
        )
        http_session = aiohttp.ClientSession(
            connector=connector,
            timeout=aiohttp.ClientTimeout(total=30),
            headers={'User-Agent': 'ThumbnailService/1.0'}
        )
        logger.info("HTTP session initialized with connection pooling")

        # Initialize thread pool for CPU-intensive tasks
        thread_pool = ThreadPoolExecutor(
            max_workers=4,  # Adjust based on CPU cores
            thread_name_prefix="thumbnail-worker"
        )
        logger.info("Thread pool initialized")

        # Initialize Playwright browser with performance optimizations
        playwright_instance = await async_playwright().start()
        browser = await playwright_instance.chromium.launch(
            headless=True,
            args=[
                '--no-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--disable-extensions',
                '--disable-default-apps',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding',
                '--disable-features=TranslateUI',
                '--disable-ipc-flooding-protection',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--memory-pressure-off',
                '--max_old_space_size=4096'
            ]
        )
        logger.info("Browser started with performance optimizations")

        # Initialize LibreOffice
        find_libreoffice()
        logger.info("Service initialization complete")

    except Exception as e:
        logger.error(f"Failed to initialize service: {e}")
        raise

    yield

    # Shutdown
    try:
        if http_session:
            await http_session.close()
            logger.info("HTTP session closed")

        if thread_pool:
            thread_pool.shutdown(wait=True)
            logger.info("Thread pool shutdown")

        if browser:
            await browser.close()
            logger.info("Browser closed")

        if playwright_instance:
            await playwright_instance.stop()
            logger.info("Playwright stopped")

        logger.info("Service shutdown complete")
    except Exception as e:
        logger.error(f"Error during shutdown: {e}")

app = FastAPI(
    title="Thumbnail Generator API",
    version="1.0.0",
    lifespan=lifespan
)


class ThumbnailRequest(BaseModel):
    url: str
    width: int = 300
    height: int = 300
    id: Optional[str] = None
    category: Optional[str] = None


async def capture_webpage_screenshot(url: str, width: int = 1280, height: int = 800) -> Optional[bytes]:
    """Optimized webpage screenshot capture"""
    global browser

    if not browser:
        logger.error("Browser not initialized")
        return None

    context = None
    page = None

    try:
        # Create context with performance optimizations
        context = await browser.new_context(
            viewport={"width": width, "height": height},
            device_scale_factor=1.0,
            java_script_enabled=True,
            ignore_https_errors=True,
            # Performance optimizations
            bypass_csp=True,
            reduced_motion='reduce',
            color_scheme='light'
        )

        # Block unnecessary resources for faster loading
        await context.route("**/*.{png,jpg,jpeg,gif,svg,ico,woff,woff2,ttf,eot}",
                            lambda route: route.abort() if 'favicon' in route.request.url else route.continue_())
        await context.route("**/*.{css}", lambda route: route.abort())
        await context.route("**/analytics*", lambda route: route.abort())
        await context.route("**/ads*", lambda route: route.abort())
        await context.route("**/tracking*", lambda route: route.abort())

        page = await context.new_page()

        # Set shorter timeouts for better performance
        page.set_default_timeout(10000)

        # Disable images and CSS for faster loading (we only need layout)
        await page.add_init_script("""
            // Disable image loading
            const originalCreateElement = document.createElement;
            document.createElement = function(tagName) {
                const element = originalCreateElement.call(this, tagName);
                if (tagName.toLowerCase() === 'img') {
                    element.style.display = 'none';
                }
                return element;
            };
        """)

        try:
            # Try fast loading first
            await page.goto(url, timeout=8000, wait_until="domcontentloaded")
            # Short wait for dynamic content
            await page.wait_for_timeout(500)
        except Exception as e:
            logger.warning(
                f"Fast load failed for {url}, trying networkidle: {e}")
            try:
                await page.goto(url, timeout=12000, wait_until="networkidle")
            except Exception as e2:
                logger.error(f"Failed to load page {url}: {e2}")
                return None

        # Take screenshot with optimized settings
        screenshot_bytes = await page.screenshot(
            type="jpeg",  # JPEG is faster than PNG
            quality=85,   # Good quality but faster
            full_page=False,
            clip={"x": 0, "y": 0, "width": width, "height": height},
        )

        return screenshot_bytes

    except Exception as e:
        logger.error(f"Failed to capture screenshot for {url}: {e}")
        return None
    finally:
        try:
            if page:
                await page.close()
            if context:
                await context.close()
        except Exception as e:
            logger.warning(f"Error closing page/context: {e}")


def process_thumbnail(screenshot_bytes: bytes, width: int = 300, height: int = 300) -> bytes:
    """Optimized thumbnail processing"""
    try:
        img = Image.open(io.BytesIO(screenshot_bytes))

        # Use faster resampling for better performance
        img.thumbnail((width, height), Image.Resampling.BILINEAR)

        if img.mode in ('RGBA', 'P'):
            img = img.convert('RGB')

        buffer = io.BytesIO()
        # Optimized JPEG settings for speed vs quality balance
        img.save(buffer, format="JPEG", quality=80,
                 optimize=True, progressive=True)
        buffer.seek(0)

        return buffer.getvalue()

    except Exception as e:
        logger.error(f"Failed to process thumbnail: {e}")
        raise


@app.get("/")
async def root():
    return {"message": "Thumbnail Generator API is running"}


@app.get("/health")
async def health_check():
    """Enhanced health check with performance metrics"""
    global browser, http_session, thread_pool

    browser_status = "healthy" if browser and not browser.is_closed() else "unhealthy"
    libreoffice_status = "healthy" if libreoffice_path and os.path.exists(
        libreoffice_path) else "unhealthy"
    http_session_status = "healthy" if http_session and not http_session.closed else "unhealthy"
    thread_pool_status = "healthy" if thread_pool and not thread_pool._shutdown else "unhealthy"

    return {
        "status": "healthy",
        "service": "thumbnail-generator-optimized",
        "browser_status": browser_status,
        "libreoffice_status": libreoffice_status,
        "http_session_status": http_session_status,
        "thread_pool_status": thread_pool_status,
        "cache_size": len(thumbnail_cache),
        "cache_max_size": MAX_CACHE_SIZE
    }


@app.post("/cache/clear")
async def clear_cache():
    """Clear the thumbnail cache"""
    global thumbnail_cache
    cache_size = len(thumbnail_cache)
    thumbnail_cache.clear()
    return {
        "success": True,
        "message": f"Cache cleared. Removed {cache_size} items."
    }


@app.get("/cache/stats")
async def cache_stats():
    """Get cache statistics"""
    total_items = len(thumbnail_cache)
    if total_items == 0:
        return {
            "total_items": 0,
            "oldest_timestamp": None,
            "newest_timestamp": None,
            "cache_utilization": 0.0
        }

    timestamps = [item['timestamp'] for item in thumbnail_cache.values()]
    oldest = min(timestamps)
    newest = max(timestamps)

    return {
        "total_items": total_items,
        "max_items": MAX_CACHE_SIZE,
        "oldest_timestamp": oldest,
        "newest_timestamp": newest,
        "cache_utilization": (total_items / MAX_CACHE_SIZE) * 100,
        "cache_ttl_seconds": CACHE_TTL
    }


@app.post("/thumbnail/")
async def generate_thumbnail(request: ThumbnailRequest):
    """Optimized thumbnail generation with caching and smart routing"""
    url = request.url
    width = request.width
    height = request.height
    id = request.id
    category = request.category

    try:
        parsed_url = urlparse(url)
        if not parsed_url.scheme or not parsed_url.netloc:
            raise HTTPException(status_code=400, detail="Invalid URL format")

        # Check cache first
        cache_key = get_cache_key(url, width, height)
        cached_result = get_cached_thumbnail(cache_key)
        if cached_result:
            logger.info(f"Cache hit for {url}")
            return {
                "success": True,
                "thumbnail_base64": cached_result["thumbnail_base64"],
                "content_type": "image/jpeg",
                "size": f"{width}x{height}",
                "url": url,
                "id": id,
                "category": category,
                "file_size_bytes": cached_result["file_size_bytes"],
                "domain": parsed_url.netloc,
                "method_used": cached_result["method_used"],
                "cached": True
            }

        thumbnail_bytes = None
        method_used = None

        # Smart routing: check if URL looks like a document first
        if is_document_url(url):
            logger.info(
                f"Document URL detected, trying document conversion first: {url}")
            file_content = await download_file(url)

            if file_content:
                logger.info(
                    "File downloaded successfully, attempting document conversion")
                thumbnail_bytes = await document_thumbnail(file_content, width, height)
                if thumbnail_bytes:
                    logger.info("Document conversion successful")
                    method_used = "document"
                else:
                    logger.warning(
                        "Document conversion failed, falling back to web page")

        # If document conversion failed or URL doesn't look like a document, try web page
        if not thumbnail_bytes:
            logger.info(f"Attempting web page screenshot for: {url}")
            # Use higher resolution for better quality, then downscale
            screenshot_bytes = await capture_webpage_screenshot(url, width * 2, height * 2)

            if screenshot_bytes:
                logger.info("Web page screenshot successful")
                thumbnail_bytes = process_thumbnail(
                    screenshot_bytes, width, height)
                method_used = "web_page"
            elif not is_document_url(url):
                # Last resort: try document conversion for non-document URLs
                logger.info(
                    "Web page failed, trying document conversion as fallback")
                file_content = await download_file(url)

                if file_content:
                    thumbnail_bytes = await document_thumbnail(file_content, width, height)
                    if thumbnail_bytes:
                        logger.info("Document conversion fallback successful")
                        method_used = "document"

        if not thumbnail_bytes:
            raise HTTPException(
                status_code=500, detail="Failed to generate thumbnail using all available methods")

        thumbnail_base64 = base64.b64encode(thumbnail_bytes).decode('utf-8')

        # Cache the result
        result_data = {
            "thumbnail_base64": thumbnail_base64,
            "file_size_bytes": len(thumbnail_bytes),
            "method_used": method_used
        }
        cache_thumbnail(cache_key, result_data)

        return {
            "success": True,
            "thumbnail_base64": thumbnail_base64,
            "content_type": "image/jpeg",
            "size": f"{width}x{height}",
            "url": url,
            "id": id,
            "category": category,
            "file_size_bytes": len(thumbnail_bytes),
            "domain": parsed_url.netloc,
            "method_used": method_used,
            "cached": False
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating thumbnail: {e}")
        raise HTTPException(
            status_code=500, detail=f"Internal server error: {str(e)}")

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
