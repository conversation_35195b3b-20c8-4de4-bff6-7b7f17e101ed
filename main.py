from pydantic import BaseModel
import io
import logging
from typing import Optional
from urllib.parse import urlparse
import base64
from enum import Enum
from contextlib import asynccontextmanager
import uvicorn
from fastapi import FastAPI, HTTPException, Query
from fastapi.responses import StreamingResponse
from PIL import Image
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, BrowserContext

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

browser: Optional[Browser] = None
playwright_instance = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage browser lifecycle - start on startup, close on shutdown"""
    global browser, playwright_instance

    # Startup
    try:
        logger.info("Starting browser...")
        playwright_instance = await async_playwright().start()
        browser = await playwright_instance.chromium.launch(
            headless=True,
            args=[
                '--no-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--disable-extensions',
                '--disable-default-apps',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding',
            ]
        )
        logger.info("Browser started successfully")
    except Exception as e:
        logger.error(f"Failed to start browser: {e}")
        raise

    yield

    # Shutdown
    try:
        if browser:
            await browser.close()
            logger.info("Browser closed")
        if playwright_instance:
            await playwright_instance.stop()
            logger.info("Playwright stopped")
    except Exception as e:
        logger.error(f"Error during shutdown: {e}")


app = FastAPI(
    title="Thumbnail Generator API",
    version="1.0.0",
    lifespan=lifespan
)


class ThumbnailRequest(BaseModel):
    url: str
    width: int = 300
    height: int = 300
    id: Optional[str] = None
    category: Optional[str] = None


async def capture_webpage_screenshot(url: str, width: int = 1280, height: int = 800) -> Optional[bytes]:
    """Capture screenshot using the shared browser instance"""
    global browser

    if not browser:
        logger.error("Browser not initialized")
        return None

    context = None
    page = None

    try:
        # Create a new context for this request (lightweight)
        context = await browser.new_context(
            viewport={"width": width, "height": height},
            device_scale_factor=1.0,
            # Additional optimizations
            java_script_enabled=True,
            ignore_https_errors=True,
        )

        page = await context.new_page()

        # Set a shorter timeout for faster failures
        page.set_default_timeout(15000)

        try:
            # Try networkidle first with shorter timeout
            await page.goto(url, timeout=15000, wait_until="networkidle")
        except Exception as e:
            logger.warning(
                f"Networkidle timeout for {url}, trying domcontentloaded: {e}")
            try:
                await page.goto(url, timeout=10000, wait_until="domcontentloaded")
            except Exception as e2:
                logger.error(f"Failed to load page {url}: {e2}")
                return None

        # Reduced wait time for dynamic content
        await page.wait_for_timeout(1000)

        # Take screenshot
        screenshot_bytes = await page.screenshot(
            type="png",
            full_page=False,
            clip={"x": 0, "y": 0, "width": width, "height": height},
        )

        return screenshot_bytes

    except Exception as e:
        logger.error(f"Failed to capture screenshot for {url}: {e}")
        return None
    finally:
        # Always clean up the context and page
        try:
            if page:
                await page.close()
            if context:
                await context.close()
        except Exception as e:
            logger.warning(f"Error closing page/context: {e}")


def process_thumbnail(screenshot_bytes: bytes, width: int = 300, height: int = 300) -> bytes:
    """Process screenshot into thumbnail - same as before but with minor optimizations"""
    try:
        img = Image.open(io.BytesIO(screenshot_bytes))

        # Use LANCZOS for better quality, but you could use BILINEAR for speed
        img.thumbnail((width, height), Image.Resampling.LANCZOS)

        if img.mode in ('RGBA', 'P'):
            img = img.convert('RGB')

        # Save to buffer with slightly lower quality for smaller size
        buffer = io.BytesIO()
        img.save(buffer, format="JPEG", quality=80, optimize=True)
        buffer.seek(0)

        return buffer.getvalue()

    except Exception as e:
        logger.error(f"Failed to process thumbnail: {e}")
        raise


@app.get("/")
async def root():
    return {"message": "Thumbnail Generator API is running"}


@app.get("/health")
async def health_check() -> dict:
    browser_status = "healthy" if browser is not None else "unhealthy"
    return {
        "service": "thumbnail-generator",
        "browser_status": browser_status,
        "playwright_status": "healthy" if playwright_instance is not None else "unhealthy",
    }


class ThumbnailFormat(str, Enum):
    image = "image"
    base64 = "base64"


@app.post("/thumbnail/")
async def generate_thumbnail(request: ThumbnailRequest):
    url = request.url
    width = request.width
    height = request.height
    id = request.id
    category = request.category

    try:
        parsed_url = urlparse(url)
        if not parsed_url.scheme or not parsed_url.netloc:
            raise HTTPException(status_code=400, detail="Invalid URL format")

        # Capture screenshot using shared browser
        logger.info(f"Capturing screenshot for: {url}")
        screenshot_bytes = await capture_webpage_screenshot(url)

        if not screenshot_bytes:
            raise HTTPException(
                status_code=500, detail="Failed to capture webpage screenshot")

        thumbnail_bytes = process_thumbnail(screenshot_bytes, width, height)

        thumbnail_base64 = base64.b64encode(thumbnail_bytes).decode('utf-8')

        return {
            "success": True,
            "thumbnail_base64": thumbnail_base64,
            "url": url,
            "id": id,
            "category": category,
            "domain": parsed_url.netloc
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating thumbnail: {e}")
        raise HTTPException(
            status_code=500, detail=f"Internal server error: {str(e)}")


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
