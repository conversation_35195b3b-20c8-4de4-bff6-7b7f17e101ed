from pydantic import BaseModel
import io
import logging
from typing import Optional
from urllib.parse import urlparse
import base64
from enum import Enum

import uvicorn
from fastapi import FastAPI, HTTPException, Query
from fastapi.responses import StreamingResponse
from PIL import Image
from playwright.async_api import async_playwright

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="Thumbnail Generator API",
              version="1.0.0")


class ThumbnailRequest(BaseModel):
    url: str
    width: int = 300
    height: int = 300
    id: Optional[str] = None
    category: Optional[str] = None


async def capture_webpage_screenshot(url: str, width: int = 1280, height: int = 800) -> Optional[bytes]:

    try:
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            context = await browser.new_context(
                viewport={"width": width, "height": height},
                device_scale_factor=1.0,
            )

            page = await context.new_page()

            try:
                # Try to load with networkidle first
                await page.goto(url, timeout=30000, wait_until="networkidle")
            except Exception as e:
                logger.warning(f"Page load timeout for {url}: {e}")
                # Fallback to domcontentloaded
                await page.goto(url, timeout=30000, wait_until="domcontentloaded")

            # Wait a bit more for dynamic content
            await page.wait_for_timeout(2000)

            # Take screenshot
            screenshot_bytes = await page.screenshot(
                type="png",
                full_page=False,
                clip={"x": 0, "y": 0, "width": width, "height": height},
            )

            await browser.close()
            return screenshot_bytes

    except Exception as e:
        logger.error(f"Failed to capture screenshot for {url}: {e}")
        return None


def process_thumbnail(screenshot_bytes: bytes, width: int = 300, height: int = 300) -> bytes:
    """
    Process the screenshot and create a thumbnail.
    """
    try:

        img = Image.open(io.BytesIO(screenshot_bytes))

        img.thumbnail((width, height), Image.Resampling.LANCZOS)

        if img.mode in ('RGBA', 'P'):
            img = img.convert('RGB')

        # Save to buffer
        buffer = io.BytesIO()
        img.save(buffer, format="JPEG", quality=85, optimize=True)
        buffer.seek(0)

        return buffer.getvalue()

    except Exception as e:
        logger.error(f"Failed to process thumbnail: {e}")
        raise


@app.get("/")
async def root():
    """Health check endpoint."""
    return {"message": "Thumbnail Generator API is running"}


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "service": "thumbnail-generator"}


class ThumbnailFormat(str, Enum):
    image = "image"
    base64 = "base64"


@app.post("/thumbnail/")
async def generate_thumbnail(request: ThumbnailRequest):
    url = request.url
    width = request.width
    height = request.height
    id = request.id
    category = request.category

    try:
        parsed_url = urlparse(url)
        if not parsed_url.scheme or not parsed_url.netloc:
            raise HTTPException(status_code=400, detail="Invalid URL format")

        # Capture screenshot
        logger.info(f"Capturing screenshot for: {url}")
        screenshot_bytes = await capture_webpage_screenshot(url)

        if not screenshot_bytes:
            raise HTTPException(
                status_code=500, detail="Failed to capture webpage screenshot")

        logger.info(f"Processing thumbnail with size: {width}x{height}")
        thumbnail_bytes = process_thumbnail(screenshot_bytes, width, height)

        thumbnail_base64 = base64.b64encode(thumbnail_bytes).decode('utf-8')

        return {
            "success": True,
            "thumbnail_base64": thumbnail_base64,
            "content_type": "image/jpeg",
            "size": f"{width}x{height}",
            "url": url,
            "id": id,
            "category": category,
            "file_size_bytes": len(thumbnail_bytes),
            "domain": parsed_url.netloc
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating thumbnail: {e}")
        raise HTTPException(
            status_code=500, detail=f"Internal server error: {str(e)}")


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
