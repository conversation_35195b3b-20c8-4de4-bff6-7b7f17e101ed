from pydantic import BaseModel
import io
import logging
import os
import tempfile
import subprocess
from typing import Optional
from urllib.parse import urlparse
import base64
from contextlib import asynccontextmanager
import uvicorn
from fastapi import FastAPI, HTTPException
from PIL import Image
from pdf2image import convert_from_path
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, BrowserContext
import aiohttp

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

browser: Optional[Browser] = None
playwright_instance = None
libreoffice_path = None


def find_libreoffice():
    """Find LibreOffice executable path"""
    global libreoffice_path

    common_paths = [
        "/usr/bin/soffice",
        "/usr/local/bin/soffice",
        "/opt/libreoffice/program/soffice",
    ]

    for path in common_paths:
        if os.path.exists(path):
            logger.info(f"Found LibreOffice at: {path}")
            libreoffice_path = path
            return path

    logger.warning(
        "LibreOffice not found in common locations, using default path")
    libreoffice_path = "/usr/bin/soffice"
    return libreoffice_path


async def download_file(url: str) -> Optional[bytes]:
    """Download file from URL"""
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url, timeout=30) as response:
                if response.status == 200:
                    return await response.read()
                else:
                    logger.warning(
                        f"Failed to download file: HTTP {response.status}")
                    return None
    except Exception as e:
        logger.warning(f"Download failed: {str(e)}")
        return None


def document_thumbnail(content: bytes, width: int = 300, height: int = 300) -> Optional[bytes]:
    """Generate thumbnail from document content"""
    global libreoffice_path

    if not libreoffice_path or not os.path.exists(libreoffice_path):
        logger.warning("LibreOffice not available for document conversion")
        return None

    try:
        with tempfile.TemporaryDirectory() as tmpdir:
            doc_path = os.path.join(tmpdir, "document")
            with open(doc_path, "wb") as f:
                f.write(content)

            # Convert to PDF
            result = subprocess.run(
                [
                    libreoffice_path,
                    "--headless",
                    "--convert-to",
                    "pdf",
                    "--outdir",
                    tmpdir,
                    doc_path,
                ],
                capture_output=True,
                timeout=120,
            )

            if result.returncode != 0:
                logger.error(f"LibreOffice failed: {result.stderr.decode()}")
                return None

            # Find generated PDF
            pdf_files = [f for f in os.listdir(tmpdir) if f.endswith(".pdf")]
            if not pdf_files:
                return None

            pdf_path = os.path.join(tmpdir, pdf_files[0])

            # Convert PDF to image
            images = convert_from_path(pdf_path, first_page=1, last_page=1)
            if not images:
                return None

            # Process the image into thumbnail
            img = images[0]
            img.thumbnail((width, height), Image.Resampling.LANCZOS)

            if img.mode in ('RGBA', 'P'):
                img = img.convert('RGB')

            # Convert to bytes
            buffer = io.BytesIO()
            img.save(buffer, format="JPEG", quality=80, optimize=True)
            buffer.seek(0)

            return buffer.getvalue()

    except Exception as e:
        logger.warning(f"Document thumbnail attempt failed: {str(e)}")
        return None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage browser lifecycle"""
    global browser, playwright_instance

    # Startup
    try:
        logger.info("Starting browser...")
        playwright_instance = await async_playwright().start()
        browser = await playwright_instance.chromium.launch(
            headless=True,
            args=[
                '--no-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--disable-extensions',
                '--disable-default-apps',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding',
            ]
        )
        logger.info("Browser started successfully")

        # Initialize LibreOffice
        find_libreoffice()

    except Exception as e:
        logger.error(f"Failed to start browser: {e}")
        raise

    yield

    # Shutdown
    try:
        if browser:
            await browser.close()
            logger.info("Browser closed")
        if playwright_instance:
            await playwright_instance.stop()
            logger.info("Playwright stopped")
    except Exception as e:
        logger.error(f"Error during shutdown: {e}")

app = FastAPI(
    title="Thumbnail Generator API",
    version="1.0.0",
    lifespan=lifespan
)


class ThumbnailRequest(BaseModel):
    url: str
    width: int = 300
    height: int = 300
    id: Optional[str] = None
    category: Optional[str] = None


async def capture_webpage_screenshot(url: str, width: int = 1280, height: int = 800) -> Optional[bytes]:
    global browser

    if not browser:
        logger.error("Browser not initialized")
        return None

    context = None
    page = None

    try:
        context = await browser.new_context(
            viewport={"width": width, "height": height},
            device_scale_factor=1.0,
            java_script_enabled=True,
            ignore_https_errors=True,
        )

        page = await context.new_page()
        page.set_default_timeout(15000)

        try:
            await page.goto(url, timeout=15000, wait_until="networkidle")
        except Exception as e:
            logger.warning(
                f"Networkidle timeout for {url}, trying domcontentloaded: {e}")
            try:
                await page.goto(url, timeout=10000, wait_until="domcontentloaded")
            except Exception as e2:
                logger.error(f"Failed to load page {url}: {e2}")
                return None

        await page.wait_for_timeout(1000)

        screenshot_bytes = await page.screenshot(
            type="png",
            full_page=False,
            clip={"x": 0, "y": 0, "width": width, "height": height},
        )

        return screenshot_bytes

    except Exception as e:
        logger.error(f"Failed to capture screenshot for {url}: {e}")
        return None
    finally:
        try:
            if page:
                await page.close()
            if context:
                await context.close()
        except Exception as e:
            logger.warning(f"Error closing page/context: {e}")


def process_thumbnail(screenshot_bytes: bytes, width: int = 300, height: int = 300) -> bytes:
    try:
        img = Image.open(io.BytesIO(screenshot_bytes))
        img.thumbnail((width, height), Image.Resampling.LANCZOS)

        if img.mode in ('RGBA', 'P'):
            img = img.convert('RGB')

        buffer = io.BytesIO()
        img.save(buffer, format="JPEG", quality=80, optimize=True)
        buffer.seek(0)

        return buffer.getvalue()

    except Exception as e:
        logger.error(f"Failed to process thumbnail: {e}")
        raise


@app.get("/")
async def root():
    """Health check endpoint."""
    return {"message": "Thumbnail Generator API is running"}


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    global browser
    browser_status = "healthy" if browser and not browser.is_closed() else "unhealthy"
    libreoffice_status = "healthy" if libreoffice_path and os.path.exists(
        libreoffice_path) else "unhealthy"

    return {
        "status": "healthy",
        "service": "thumbnail-generator",
        "browser_status": browser_status,
        "libreoffice_status": libreoffice_status
    }


@app.post("/thumbnail/")
async def generate_thumbnail(request: ThumbnailRequest):
    """Generate thumbnail - tries both web page and document conversion"""
    url = request.url
    width = request.width
    height = request.height
    id = request.id
    category = request.category

    try:
        parsed_url = urlparse(url)
        if not parsed_url.scheme or not parsed_url.netloc:
            raise HTTPException(status_code=400, detail="Invalid URL format")

        thumbnail_bytes = None
        method_used = None

        # First try: Web page screenshot
        logger.info(f"Attempting web page screenshot for: {url}")
        screenshot_bytes = await capture_webpage_screenshot(url, width * 2, height * 2)

        if screenshot_bytes:
            logger.info("Web page screenshot successful")
            thumbnail_bytes = process_thumbnail(
                screenshot_bytes, width, height)
            method_used = "web_page"
        else:
            # Second try: Document conversion
            logger.info(
                f"Web page failed, trying document conversion for: {url}")
            file_content = await download_file(url)

            if file_content:
                logger.info(
                    "File downloaded successfully, attempting document conversion")
                thumbnail_bytes = document_thumbnail(
                    file_content, width, height)
                if thumbnail_bytes:
                    logger.info("Document conversion successful")
                    method_used = "document"
                else:
                    logger.warning("Document conversion failed")
            else:
                logger.warning("File download failed")

        if not thumbnail_bytes:
            raise HTTPException(
                status_code=500, detail="Failed to generate thumbnail using both methods")

        thumbnail_base64 = base64.b64encode(thumbnail_bytes).decode('utf-8')

        return {
            "success": True,
            "thumbnail_base64": thumbnail_base64,
            "content_type": "image/jpeg",
            "size": f"{width}x{height}",
            "url": url,
            "id": id,
            "category": category,
            "file_size_bytes": len(thumbnail_bytes),
            "domain": parsed_url.netloc,
            "method_used": method_used
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating thumbnail: {e}")
        raise HTTPException(
            status_code=500, detail=f"Internal server error: {str(e)}")

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
